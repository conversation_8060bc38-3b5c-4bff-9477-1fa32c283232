# Mensajes del plugin CrazyRewards
messages:
  # Prefijo para todos los mensajes
  prefix: '&b[CrazyRewards] &7'
  
  # Mensajes generales
  no-permission: '&cNo tienes permisos para usar este comando.'
  player-only: '&cEste comando solo puede ser usado por jugadores.'
  reload-success: '&aConfiguración recargada correctamente.'
  
  # Mensajes de recompensas
  reward-claimed: '&a¡Has reclamado la recompensa &e{reward}&a!'
  reward-cooldown: '&cDebes esperar &e{time} &cpara reclamar esta recompensa.'
  reward-no-permission: '&cNo tienes permisos para reclamar esta recompensa.'
  reward-error: '&cHubo un error al procesar tu recompensa. Contacta a un administrador.'
  
  # Mensajes de login
  login-reminder: '&aTienes recompensas disponibles! Usa &e/rewards &apara verlas.'
  
  # Mensajes de administración
  admin-loot-saved: '&aLoot guardado para el slot &e{slot}&a.'
  admin-loot-no-item: '&cDebes tener un ítem en tu mano para guardar el loot.'
  admin-invalid-slot: '&cSlot inválido. Debe ser un número entre 0 y 44.'
  admin-slot-not-configured: '&cEl slot &e{slot} &cno está configurado en rewardmenu.yml.'
  
  # Mensajes de error
  database-error: '&cError de base de datos. Contacta a un administrador.'
  config-error: '&cError en la configuración. Revisa la consola para más detalles.'
  
  # Mensajes de debug
  debug-reward-claim: '&7[DEBUG] Jugador {player} reclamó recompensa {reward}'
  debug-cooldown-check: '&7[DEBUG] Verificando cooldown para {player} en {reward}: {result}'
  debug-database-query: '&7[DEBUG] Ejecutando query: {query}'
