config-version: 1

# Nombre del menú de recompensas
menu:
  title: '     &8ᴍᴇɴú ᴅᴇ &8&lʀᴇᴄᴏᴍᴘᴇɴꜱᴀꜱ'
  size: 45
  filler-item:
    enabled: true
    material: BLACK_STAINED_GLASS_PANE
    name: '&f'
    lore: []

# Al cerrar el inventario tras reclamar
close-on-claim: false

# Avisar si el jugador tiene recompensas disponibles al entrar
login-reminder:
  enabled: true
  delay: 3

# Activar debug mode (registra más en consola)
debug: false

# Formato de cooldown digital
cooldown-format:
  short: '{hours}h {minutes}m'
  full: '{days}d {hours}h {minutes}m'

# Configuración de MySQL
mysql:
  host: 'localhost'
  port: 3306
  database: 'crazyrewards'
  username: 'root'
  password: 'password'
  pool-size: 10
  use-ssl: false

# Prefijo para mensajes en consola o chat
prefix: '&b[CrazyRewards] &7'

# Reintentos antes de deshabilitar conexión fallida
mysql-retry-limit: 3

# Cache settings
cache:
  # Tiempo en segundos para mantener cooldowns en cache
  cooldown-cache-time: 300
  # Tiempo en segundos para mantener datos de jugador en cache
  player-data-cache-time: 600
