package org.contrum.crazy.model;

import org.bukkit.Material;
import java.util.List;

/**
 * Modelo que representa el ítem visual de una recompensa
 */
public class RewardItem {
    private final String name;
    private final Material material;
    private final List<String> lore;
    private final int amount;
    private final short durability;

    public RewardItem(String name, Material material, List<String> lore) {
        this(name, material, lore, 1, (short) 0);
    }

    public RewardItem(String name, Material material, List<String> lore, int amount, short durability) {
        this.name = name;
        this.material = material;
        this.lore = lore;
        this.amount = amount;
        this.durability = durability;
    }

    public String getName() {
        return name;
    }

    public Material getMaterial() {
        return material;
    }

    public List<String> getLore() {
        return lore;
    }

    public int getAmount() {
        return amount;
    }

    public short getDurability() {
        return durability;
    }

    @Override
    public String toString() {
        return "RewardItem{" +
                "name='" + name + '\'' +
                ", material=" + material +
                ", amount=" + amount +
                '}';
    }
}
