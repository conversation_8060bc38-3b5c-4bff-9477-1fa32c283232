package org.contrum.crazy.model;

import org.bukkit.Material;
import java.util.List;

/**
 * Modelo que representa una recompensa
 */
public class Reward {
    private final String id;
    private final int slot;
    private final long cooldown;
    private final String permission;
    private final RewardItem item;
    private final List<String> commands;

    public Reward(String id, int slot, long cooldown, String permission, RewardItem item, List<String> commands) {
        this.id = id;
        this.slot = slot;
        this.cooldown = cooldown;
        this.permission = permission;
        this.item = item;
        this.commands = commands;
    }

    public String getId() {
        return id;
    }

    public int getSlot() {
        return slot;
    }

    public long getCooldown() {
        return cooldown;
    }

    public String getPermission() {
        return permission;
    }

    public RewardItem getItem() {
        return item;
    }

    public List<String> getCommands() {
        return commands;
    }

    @Override
    public String toString() {
        return "Reward{" +
                "id='" + id + '\'' +
                ", slot=" + slot +
                ", cooldown=" + cooldown +
                ", permission='" + permission + '\'' +
                '}';
    }
}
